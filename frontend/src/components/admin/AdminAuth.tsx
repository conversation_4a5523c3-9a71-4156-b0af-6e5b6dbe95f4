import * as React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Lock, Mail, Shield } from 'lucide-react';

interface AdminAuthProps {
  onAuth: (token: string) => void;
  pbUrl: string;
}

export const AdminAuth: React.FC<AdminAuthProps> = ({ onAuth, pbUrl }) => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  const [showPassword, setShowPassword] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Сначала пытаемся авторизоваться как админ
      let response = await fetch(`${pbUrl}/api/admins/auth-with-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ identity: email, password }),
      });

      let data = await response.json();

      // Если не получилось как админ, пытаемся как обычный пользователь
      if (!response.ok) {
        response = await fetch(`${pbUrl}/api/collections/users/auth-with-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ identity: email, password }),
        });
        data = await response.json();
      }

      if (response.ok && data.token) {
        // Сохраняем токен в localStorage
        localStorage.setItem('pb_token', data.token);

        // Устанавливаем cookie для middleware
        document.cookie = `pb_token=${data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;

        onAuth(data.token);
      } else {
        setError(data.message || 'Неверный email или пароль');
      }
    } catch (err) {
      setError('Ошибка подключения к серверу');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5 p-4">
      <Card className="w-full max-w-md shadow-xl border-0 bg-white/95 backdrop-blur-sm">
        <CardHeader className="text-center space-y-4 pb-6">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-[#8BC34A] to-[#4E8C29] rounded-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Панель администратора
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              Войдите для управления контентом сайта
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="pl-10 border-gray-300 focus:border-[#8BC34A] focus:ring-[#8BC34A]"
                  required
                  disabled={loading}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Пароль
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  className="pl-10 pr-10 border-gray-300 focus:border-[#8BC34A] focus:ring-[#8BC34A]"
                  required
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full bg-[#8BC34A] hover:bg-[#4E8C29] text-white font-medium py-2.5 transition-colors duration-200"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Вход...
                </div>
              ) : (
                'Войти в систему'
              )}
            </Button>
          </form>

          <div className="text-center text-xs text-gray-500 border-t pt-4">
            Система управления контентом<br />
            Стоматологическая клиника "Стом-Лайн"
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
