import * as React from 'react';
import { AdminAuth } from './AdminAuth';
import { AdminNotifications, useAdminNotifications } from './AdminNotifications';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  FileText,
  Tag,
  MessageSquare,
  HelpCircle,
  Newspaper,
  DollarSign,
  Settings,
  LogOut,
  Edit,
  Plus,
  BarChart3
} from 'lucide-react';

interface AdminDashboardProps {
  isAuthenticated: boolean;
  pbUrl: string;
}

interface CollectionStats {
  doctors: number;
  services: number;
  promos: number;
  reviews: number;
  faq: number;
  news: number;
  pages: number;
  prices: number;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({
  isAuthenticated: serverAuth,
  pbUrl
}) => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(serverAuth);
  const [loading, setLoading] = React.useState(false);
  const [stats, setStats] = React.useState<CollectionStats | null>(null);
  const [token, setToken] = React.useState<string>('');
  const notifications = useAdminNotifications();

  React.useEffect(() => {
    const savedToken = localStorage.getItem('pb_token');
    if (savedToken) {
      setToken(savedToken);
      setIsAuthenticated(true);
      loadStats(savedToken);
    }
  }, []);

  const loadStats = async (authToken: string) => {
    setLoading(true);
    try {
      const collections = ['doctors', 'services', 'promos', 'reviews', 'faq', 'news', 'pages', 'prices'];
      const statsData: Partial<CollectionStats> = {};

      await Promise.all(
        collections.map(async (collection) => {
          try {
            const response = await fetch(`${pbUrl}/api/collections/${collection}/records?perPage=1`, {
              headers: {
                'Authorization': `Bearer ${authToken}`,
              },
            });
            if (response.ok) {
              const data = await response.json();
              statsData[collection as keyof CollectionStats] = data.totalItems || 0;
            }
          } catch (error) {
            console.error(`Error loading ${collection} stats:`, error);
            statsData[collection as keyof CollectionStats] = 0;
          }
        })
      );

      setStats(statsData as CollectionStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAuth = (authToken: string) => {
    setToken(authToken);
    setIsAuthenticated(true);
    loadStats(authToken);
    notifications.success('Авторизация успешна', 'Добро пожаловать в админ-панель!');
  };

  const handleLogout = () => {
    localStorage.removeItem('pb_token');
    document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    setToken('');
    setIsAuthenticated(false);
    setStats(null);
    notifications.info('Выход выполнен', 'До свидания!');
  };

  if (!isAuthenticated) {
    return <AdminAuth onAuth={handleAuth} pbUrl={pbUrl} />;
  }

  const collections = [
    {
      name: 'doctors',
      title: 'Специалисты',
      description: 'Управление информацией о врачах',
      icon: Users,
      color: 'bg-blue-500',
      count: stats?.doctors || 0,
    },
    {
      name: 'services',
      title: 'Услуги',
      description: 'Каталог медицинских услуг',
      icon: FileText,
      color: 'bg-green-500',
      count: stats?.services || 0,
    },
    {
      name: 'promos',
      title: 'Акции',
      description: 'Специальные предложения',
      icon: Tag,
      color: 'bg-orange-500',
      count: stats?.promos || 0,
    },
    {
      name: 'reviews',
      title: 'Отзывы',
      description: 'Отзывы пациентов',
      icon: MessageSquare,
      color: 'bg-purple-500',
      count: stats?.reviews || 0,
    },
    {
      name: 'faq',
      title: 'FAQ',
      description: 'Часто задаваемые вопросы',
      icon: HelpCircle,
      color: 'bg-yellow-500',
      count: stats?.faq || 0,
    },
    {
      name: 'news',
      title: 'Новости',
      description: 'Новости и статьи',
      icon: Newspaper,
      color: 'bg-red-500',
      count: stats?.news || 0,
    },
    {
      name: 'pages',
      title: 'Страницы',
      description: 'Статические страницы сайта',
      icon: FileText,
      color: 'bg-indigo-500',
      count: stats?.pages || 0,
    },
    {
      name: 'prices',
      title: 'Прайс-лист',
      description: 'Цены на услуги',
      icon: DollarSign,
      color: 'bg-emerald-500',
      count: stats?.prices || 0,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-[#8BC34A] to-[#4E8C29] rounded-lg flex items-center justify-center">
                <Settings className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Панель администратора</h1>
                <p className="text-sm text-gray-500">Стом-Лайн</p>
              </div>
            </div>
            <Button 
              onClick={handleLogout}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Выйти
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Обзор контента</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {collections.map((collection) => (
              <Card key={collection.name} className="hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">
                    {collection.title}
                  </CardTitle>
                  <div className={`w-8 h-8 ${collection.color} rounded-lg flex items-center justify-center`}>
                    <collection.icon className="w-4 h-4 text-white" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {loading ? '...' : collection.count}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {collection.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Collections Management */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Управление контентом</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {collections.map((collection) => (
              <Card key={collection.name} className="hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 ${collection.color} rounded-lg flex items-center justify-center`}>
                      <collection.icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{collection.title}</CardTitle>
                      <CardDescription>{collection.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Записей:</span>
                    <Badge variant="secondary">
                      {loading ? '...' : collection.count}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1 bg-[#8BC34A] hover:bg-[#4E8C29]"
                      onClick={() => {
                        if (['doctors', 'services', 'reviews'].includes(collection.name)) {
                          window.location.href = `/admin/${collection.name}`;
                        } else {
                          window.open(`https://pb.stom-line.ru/_/#/collections?collectionId=pbc_${collection.name}`, '_blank');
                        }
                      }}
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      Управлять
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Быстрые действия</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              className="h-16 bg-[#8BC34A] hover:bg-[#4E8C29] text-left justify-start"
              onClick={() => window.open('https://pb.stom-line.ru/_/', '_blank')}
            >
              <Settings className="w-6 h-6 mr-3" />
              <div>
                <div className="font-medium">PocketBase Admin</div>
                <div className="text-sm opacity-90">Полная админ-панель</div>
              </div>
            </Button>
            <Button 
              variant="outline"
              className="h-16 text-left justify-start border-[#8BC34A] text-[#8BC34A] hover:bg-[#8BC34A]/10"
              onClick={() => window.open('/', '_blank')}
            >
              <BarChart3 className="w-6 h-6 mr-3" />
              <div>
                <div className="font-medium">Просмотр сайта</div>
                <div className="text-sm opacity-70">Открыть главную страницу</div>
              </div>
            </Button>
            <Button 
              variant="outline"
              className="h-16 text-left justify-start border-gray-300 text-gray-700 hover:bg-gray-50"
              onClick={() => window.location.reload()}
            >
              <Settings className="w-6 h-6 mr-3" />
              <div>
                <div className="font-medium">Обновить данные</div>
                <div className="text-sm opacity-70">Перезагрузить статистику</div>
              </div>
            </Button>
          </div>
        </div>
      </main>

      {/* Notifications */}
      <AdminNotifications
        notifications={notifications.notifications}
        onRemove={notifications.removeNotification}
      />
    </div>
  );
};
