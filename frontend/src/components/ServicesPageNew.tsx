import * as React from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  ArrowRight, 
  Check, 
  Star, 
  Calendar, 
  Phone, 
  Clock,
  Heart,
  Shield,
  Award,
  Stethoscope,
  Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { EditButton } from '@/components/admin/EditButton';
import { type Service } from '@/lib/api';

interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  services: Service[];
}

interface ServicesPageNewProps {
  services: Service[];
  specialist?: string;
  isAuthenticated?: boolean;
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 60 },
  visible: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.6, 
      ease: [0.25, 0.25, 0, 1] 
    } 
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.2
    }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1, 
    transition: { 
      duration: 0.5, 
      ease: [0.25, 0.25, 0, 1] 
    } 
  }
};

export const ServicesPageNew: React.FC<ServicesPageNewProps> = ({
  services,
  specialist,
  isAuthenticated = false
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' });
  const [specialistName, setSpecialistName] = React.useState<string>('');

  // Получаем имя специалиста, если есть ID
  React.useEffect(() => {
    const fetchSpecialistName = async () => {
      if (specialist) {
        try {
          const response = await fetch(`${import.meta.env.PUBLIC_API_URL}/api/collections/doctors/records/${specialist}`);
          if (response.ok) {
            const data = await response.json();
            setSpecialistName(`${data.surname} ${data.name} ${data.patronymic || ''}`.trim());
          }
        } catch (error) {
          console.error('Ошибка при получении имени специалиста:', error);
        }
      }
    };

    fetchSpecialistName();
  }, [specialist]);

  // Группируем услуги по категориям
  const servicesByCategory = React.useMemo(() => {
    const categories = new Map<string, ServiceCategory>();

    services.forEach(service => {
      const categoryData = service.expand?.category;
      const categoryId = categoryData?.id || 'other';
      const categoryName = categoryData?.name || 'Другие услуги';

      if (!categories.has(categoryId)) {
        categories.set(categoryId, {
          id: categoryId,
          name: categoryName,
          slug: categoryData?.slug || 'other',
          description: categoryData?.description,
          image: categoryData?.image,
          services: []
        });
      }

      categories.get(categoryId)!.services.push(service);
    });

    return Array.from(categories.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [services]);

  return (
    <div className="relative flex min-h-screen flex-col bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]" />

      {/* Hero Section */}
      <section className="relative z-10 py-12 sm:py-20 md:py-32">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div 
            className="text-center"
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
          >
            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-olive-100/80 to-olive-200/80 px-4 py-2 text-xs sm:text-sm font-medium text-olive-700 backdrop-blur-sm shadow-lg border border-olive-200/50 mb-4 sm:mb-6">
              <Stethoscope className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              {specialist && specialistName ? `Услуги специалиста` : 'Наши услуги'}
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-4 sm:mb-6">
              {specialist && specialistName 
                ? specialistName
                : 'Качественная стоматология'
              }
            </h1>
            
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-olive-700 max-w-3xl mx-auto mb-6 sm:mb-8">
              {specialist && specialistName
                ? `Полный спектр стоматологических услуг от ${specialistName}`
                : 'Современные технологии и индивидуальный подход для здоровья вашей улыбки'
              }
            </p>
            
            {!specialist && (
              <motion.div 
                className="flex flex-wrap justify-center gap-3 sm:gap-6 text-olive-600"
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.div variants={scaleIn} className="flex items-center gap-1 sm:gap-2 bg-white/60 rounded-full px-2 sm:px-4 py-1 sm:py-2 backdrop-blur-sm text-xs sm:text-sm">
                  <Shield className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>Гарантия качества</span>
                </motion.div>
                <motion.div variants={scaleIn} className="flex items-center gap-1 sm:gap-2 bg-white/60 rounded-full px-2 sm:px-4 py-1 sm:py-2 backdrop-blur-sm text-xs sm:text-sm">
                  <Award className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>Опытные врачи</span>
                </motion.div>
                <motion.div variants={scaleIn} className="flex items-center gap-1 sm:gap-2 bg-white/60 rounded-full px-2 sm:px-4 py-1 sm:py-2 backdrop-blur-sm text-xs sm:text-sm">
                  <Heart className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>Индивидуальный подход</span>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Services by Categories */}
      <section ref={containerRef} className="relative z-10 py-20 md:py-32">
        <div className="container mx-auto px-4 md:px-6">
          {servicesByCategory.length > 0 ? (
            <div className="space-y-20">
              {servicesByCategory.map((category, categoryIndex) => (
                <motion.div 
                  key={category.id}
                  initial={{ opacity: 0, y: 60 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
                  transition={{ duration: 0.6, delay: categoryIndex * 0.2 }}
                  className="relative"
                >
                  {/* Category Header */}
                  <div className="text-center mb-10 sm:mb-16 relative">
                    <EditButton
                      collection="service_categories"
                      id={category.id}
                      position="top-right"
                      variant="text"
                      size="sm"
                      className="absolute top-0 right-0 bg-white/90 border border-gray-200 shadow-sm"
                      isAuthenticated={isAuthenticated}
                    />
                    
                    <div className="inline-flex items-center rounded-full bg-olive-200/70 px-2 sm:px-3 py-1 text-xs sm:text-sm text-olive-700 backdrop-blur-sm mb-4 sm:mb-6">
                      <Sparkles className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      Категория услуг
                    </div>
                    
                    <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-olive-900 mb-4 sm:mb-6">
                      {category.name}
                    </h2>
                    
                    {category.description && (
                      <div 
                        className="text-sm sm:text-base md:text-lg text-olive-700 max-w-3xl mx-auto prose prose-sm sm:prose prose-olive max-w-none"
                        dangerouslySetInnerHTML={{ __html: category.description }}
                      />
                    )}
                  </div>

                  {/* Services Grid */}
                  <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8"
                    variants={staggerContainer}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.1 }}
                  >
                    {category.services.map((service, serviceIndex) => (
                      <motion.div 
                        key={service.id} 
                        variants={fadeInUp}
                        className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:shadow-olive-300/20"
                      >
                        <EditButton
                          collection="services"
                          id={service.id}
                          position="top-right"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 border border-gray-200 shadow-sm z-20"
                          isAuthenticated={isAuthenticated}
                        />
                        
                        <div className="relative z-10 rounded-xl bg-white/60 transition-all duration-300 group-hover:bg-white/80 overflow-hidden">
                          {service.image && (
                            <div className="aspect-video overflow-hidden rounded-t-xl">
                              <img
                                src={`${import.meta.env.PUBLIC_API_URL}/api/files/services/${service.id}/${service.image}`}
                                alt={service.name}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                              />
                            </div>
                          )}
                          
                          <div className="p-3 sm:p-4 md:p-6">
                            <div className="flex items-start justify-between mb-2 sm:mb-4">
                              <h3 className="text-base sm:text-lg md:text-xl font-bold text-olive-800 group-hover:text-olive-700 transition-colors">
                                {service.name}
                              </h3>
                              
                              {service.is_featured && (
                                <Badge className="bg-olive-100/80 text-olive-800 border-0 ml-2 text-xs">
                                  <Star className="w-2 h-2 sm:w-3 sm:h-3 mr-1 fill-current" />
                                  ТОП
                                </Badge>
                              )}
                            </div>
                            
                            {service.short_description && (
                              <div 
                                className="text-olive-700 mb-3 sm:mb-4 md:mb-6 prose prose-xs sm:prose-sm prose-olive max-w-none line-clamp-3 text-xs sm:text-sm"
                                dangerouslySetInnerHTML={{ __html: service.short_description }}
                              />
                            )}
                            
                            <div className="flex items-center gap-2 sm:gap-3">
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="border-olive-300 text-olive-700 hover:bg-olive-50 flex-1 text-xs sm:text-sm py-1 h-8 sm:h-9"
                                onClick={() => window.location.href = `/services/${service.slug}`}
                              >
                                Подробнее
                                <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                              </Button>
                              
                              <Button 
                                size="sm"
                                className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white text-xs sm:text-sm py-1 h-8 sm:h-9 shadow-lg"
                                onClick={() => window.location.href = '/#appointment'}
                              >
                                <Calendar className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                                <span className="hidden xs:inline">Записаться</span>
                                <span className="inline xs:hidden">Запись</span>
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Animated gradient border */}
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-olive-400 via-olive-300 to-olive-400 opacity-0 transition-opacity duration-300 group-hover:opacity-100" 
                             style={{
                               backgroundSize: "200% 100%",
                               animation: "gradientMove 2s linear infinite",
                             }}
                        />
                      </motion.div>
                    ))}
                  </motion.div>
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div 
              className="text-center py-20"
              initial={{ opacity: 0, y: 60 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="max-w-md mx-auto bg-white/60 rounded-2xl p-8 backdrop-blur-sm">
                <div className="w-24 h-24 mx-auto mb-6 bg-olive-100/80 rounded-full flex items-center justify-center">
                  <Heart className="w-12 h-12 text-olive-600" />
                </div>
                <h3 className="text-2xl font-bold text-olive-900 mb-4">
                  {specialist ? 'У данного специалиста пока нет услуг' : 'Услуги загружаются'}
                </h3>
                <p className="text-olive-700 mb-6">
                  {specialist 
                    ? 'Возможно, информация об услугах обновляется. Обратитесь к администратору.'
                    : 'Пожалуйста, подождите, пока загрузятся данные об услугах.'
                  }
                </p>
                <Button 
                  className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl"
                  onClick={() => window.location.href = '/specialists'}
                >
                  Посмотреть специалистов
                </Button>
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      {!specialist && (
        <section className="bg-gradient-to-r from-olive-600 to-olive-700 text-white py-10 sm:py-16">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <div>
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6">
                Готовы к лечению?
              </h2>
              <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 opacity-90 max-w-2xl mx-auto">
                Запишитесь на консультацию к нашим специалистам. 
                Мы поможем вам обрести здоровую и красивую улыбку.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                <Button 
                  size="lg" 
                  className="bg-white text-olive-700 hover:bg-olive-50 text-sm sm:text-base py-2 h-10 sm:h-12 shadow-lg"
                  onClick={() => window.location.href = '/#appointment'}
                >
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  Записаться на прием
                </Button>
                <Button 
                  size="lg" 
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-olive-700 text-sm sm:text-base py-2 h-10 sm:h-12 shadow-lg"
                  onClick={() => window.location.href = 'tel:+78152525708'}
                >
                  <Phone className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  Позвонить сейчас
                </Button>
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};
