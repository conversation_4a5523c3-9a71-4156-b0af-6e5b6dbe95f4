---
import Layout from '../../layouts/Layout.astro';
import PocketBase from 'pocketbase';
import { Button } from '../../components/ui/button';
import { ArrowLeftIcon, ImageIcon } from 'lucide-react';
import EditButtonServer from '@/components/admin/EditButtonServer.astro';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

export async function getStaticPaths() {
  const pb = new PocketBase(PUBLIC_API_URL);

  try {
    const pages = await pb.collection('pages').getFullList({
      filter: 'is_published = true'
    });

    return pages.map((page) => ({
      params: { slug: page.slug || page.id },
      props: { page },
    }));
  } catch (error) {
    console.error('Ошибка при получении данных о страницах:', error);
    return [];
  }
}

const { page } = Astro.props;

// Функция для получения URL изображения
const getImageUrl = (page: any, filename: string) => {
  if (!filename) return null;
  return `${PUBLIC_API_URL}/api/files/pages/${page.id}/${filename}`;
};

// Функция для получения URL главного изображения
const getFeaturedImageUrl = (page: any) => {
  if (!page.featured_image) return null;
  return `${PUBLIC_API_URL}/api/files/pages/${page.id}/${page.featured_image}`;
};

// Функция для получения галереи изображений
const getGalleryImages = (page: any) => {
  if (!page.gallery) return [];
  
  // Если gallery - строка, разбиваем по запятым
  if (typeof page.gallery === 'string') {
    return page.gallery.split(',').map((filename: string) => ({
      filename: filename.trim(),
      url: getImageUrl(page, filename.trim())
    }));
  }
  
  // Если gallery - массив
  if (Array.isArray(page.gallery)) {
    return page.gallery.map((filename: string) => ({
      filename,
      url: getImageUrl(page, filename)
    }));
  }
  
  return [];
};

const galleryImages = getGalleryImages(page);
---

<Layout 
  title={page.meta_title || page.title}
  description={page.meta_description || `Страница: ${page.title}`}
>
  <div class="min-h-screen bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
    <!-- Background elements -->
    <div class="pointer-events-none absolute inset-0 z-0 overflow-hidden">
      <div class="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]"></div>
      <div class="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]"></div>
      <div class="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]"></div>
    </div>

    <!-- Grid lines -->
    <div class="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]"></div>

    <div class="relative z-10 container mx-auto px-4 py-16">
      <!-- Navigation -->
      <div class="mb-8">
        <Button variant="outline" asChild class="border-olive-200 text-olive-700 hover:bg-olive-50">
          <a href="/">
            <ArrowLeftIcon class="mr-2 h-4 w-4" />
            На главную
          </a>
        </Button>
      </div>

      <!-- Page Content -->
      <article class="max-w-4xl mx-auto">
        <!-- Header -->
        <header class="mb-8">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            {page.title}
          </h1>

          <!-- Edit Button -->
          <div class="mb-6">
            <EditButtonServer collection="pages" id={page.id} />
          </div>
        </header>

        <!-- Featured Image -->
        {getFeaturedImageUrl(page) && (
          <div class="mb-8 rounded-2xl overflow-hidden shadow-2xl">
            <img
              src={getFeaturedImageUrl(page)}
              alt={page.title}
              class="w-full h-64 md:h-96 object-cover"
            />
          </div>
        )}

        <!-- Content -->
        <div class="prose prose-lg max-w-none mb-12">
          <div
            style="color: #4b5563;"
            class="leading-relaxed"
            set:html={page.content}
          />
        </div>

        <!-- Gallery -->
        {galleryImages.length > 0 && (
          <div class="mb-12">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <ImageIcon class="h-6 w-6 text-olive-500" />
              Галерея
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {galleryImages.map((image, index) => (
                <div 
                  key={index}
                  class="relative group cursor-pointer rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                  onclick={`openImageModal('${image.url}', '${page.title}')`}
                >
                  <img
                    src={image.url}
                    alt={`${page.title} - изображение ${index + 1}`}
                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                </div>
              ))}
            </div>
          </div>
        )}

        <!-- Call to Action -->
        <div class="mt-16">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-olive-100 text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              Нужна консультация?
            </h3>
            <p class="text-gray-600 mb-6">
              Свяжитесь с нами для получения подробной информации и записи на прием
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild class="bg-olive-500 hover:bg-olive-600 text-white">
                <a href="/contact">Связаться с нами</a>
              </Button>
              <Button asChild variant="outline" class="border-olive-200 text-olive-700 hover:bg-olive-50">
                <a href="/services">Наши услуги</a>
              </Button>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>

  <!-- Image Modal -->
  <div id="imageModal" class="fixed inset-0 z-50 hidden bg-black/80 backdrop-blur-sm">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="relative max-w-4xl max-h-[90vh] w-full">
        <button
          onclick="closeImageModal()"
          class="absolute -top-12 right-0 text-white hover:text-gray-300 text-xl font-bold z-10"
        >
          ✕ Закрыть
        </button>
        <img
          id="modalImage"
          src=""
          alt=""
          class="w-full h-auto max-h-[80vh] object-contain rounded-lg"
        />
      </div>
    </div>
  </div>
</Layout>

<style>
  .prose {
    color: #4b5563;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    color: #111827;
    font-weight: bold;
  }

  .prose h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .prose p {
    margin-bottom: 1rem;
    line-height: 1.625;
  }

  .prose ul,
  .prose ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  .prose li {
    margin-bottom: 0.5rem;
  }

  .prose a {
    color: #6da832;
    text-decoration: underline;
  }

  .prose a:hover {
    color: #4D8C29;
  }

  .prose blockquote {
    border-left: 4px solid #b8d7a8;
    padding-left: 1rem;
    font-style: italic;
    color: #4b5563;
    margin: 1.5rem 0;
  }

  .prose img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin: 1.5rem 0;
  }
</style>

<script>
  function openImageModal(src: string, alt: string) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage') as HTMLImageElement;
    
    if (modal && modalImage) {
      modalImage.src = src;
      modalImage.alt = alt;
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
  }
  
  function closeImageModal() {
    const modal = document.getElementById('imageModal');
    
    if (modal) {
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
  }
  
  // Закрытие модального окна по клику вне изображения
  document.getElementById('imageModal')?.addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
      closeImageModal();
    }
  });
  
  // Закрытие модального окна по нажатию Escape
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeImageModal();
    }
  });
</script>
